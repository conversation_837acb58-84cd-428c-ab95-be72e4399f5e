<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('restorant_id');
            $table->unsignedBigInteger('client_id');
            $table->unsignedBigInteger('driver_id')->nullable();
            $table->unsignedBigInteger('table_id')->nullable();
            $table->unsignedBigInteger('address_id')->nullable();
            $table->unsignedBigInteger('employee_id')->nullable();
            $table->float('order_price', 16, 2)->default(0);
            $table->float('delivery_price', 16, 2)->default(0);
            $table->float('fee_value', 16, 2)->default(0);
            $table->float('static_fee', 16, 2)->default(0);
            $table->float('vatvalue', 16, 2)->default(0);
            $table->float('discount', 16, 2)->default(0);
            $table->string('payment_method')->nullable();
            $table->string('payment_status')->default('unpaid');
            $table->string('stripe_payment_id')->nullable();
            $table->string('comment')->nullable();
            $table->string('phone')->nullable();
            $table->string('whatsapp_phone')->nullable();
            $table->integer('delivery_method')->default(1);
            $table->string('delivery_pickup_interval')->nullable();
            $table->integer('kds_finished')->default(0);
            $table->string('coupon')->nullable();
            $table->float('payment_processor_fee', 8, 2)->default(0);
            $table->timestamps();
            
            $table->foreign('restorant_id')->references('id')->on('companies');
            $table->foreign('client_id')->references('id')->on('users');
            $table->foreign('driver_id')->references('id')->on('users');
            $table->foreign('table_id')->references('id')->on('tables');
            $table->foreign('address_id')->references('id')->on('address');
            $table->foreign('employee_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
