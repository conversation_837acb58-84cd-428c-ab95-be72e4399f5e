<?php

/**
 * Test script to verify database setup and foreign key constraints
 * Run this script to test if the migration and seeding issues are resolved
 */

require_once 'vendor/autoload.php';

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Klozza Database Setup Test ===\n\n";

try {
    // Step 1: Drop all tables (purge database)
    echo "1. Purging database...\n";
    \Illuminate\Support\Facades\Artisan::call('migrate:fresh', ['--force' => true]);
    echo "   ✓ Database purged successfully\n\n";

    // Step 2: Run migrations
    echo "2. Running migrations...\n";
    \Illuminate\Support\Facades\Artisan::call('migrate', ['--force' => true]);
    echo "   ✓ Migrations completed successfully\n\n";

    // Step 3: Check if companies table exists
    echo "3. Checking table structure...\n";
    if (\Illuminate\Support\Facades\Schema::hasTable('companies')) {
        echo "   ✓ Companies table exists\n";
    } else {
        echo "   ✗ Companies table missing\n";
    }

    if (\Illuminate\Support\Facades\Schema::hasTable('restorants')) {
        echo "   ✗ Restorants table exists (should not exist)\n";
    } else {
        echo "   ✓ Restorants table correctly does not exist\n";
    }

    if (\Illuminate\Support\Facades\Schema::hasTable('simple_delivery_areas')) {
        echo "   ✓ Simple delivery areas table exists\n";
    } else {
        echo "   ✗ Simple delivery areas table missing\n";
    }

    // Step 4: Run seeders
    echo "\n4. Running seeders...\n";
    \Illuminate\Support\Facades\Artisan::call('db:seed', ['--force' => true]);
    echo "   ✓ Seeders completed successfully\n\n";

    // Step 5: Verify data
    echo "5. Verifying data integrity...\n";
    
    $companiesCount = \Illuminate\Support\Facades\DB::table('companies')->count();
    echo "   Companies count: $companiesCount\n";
    
    $deliveryAreasCount = \Illuminate\Support\Facades\DB::table('simple_delivery_areas')->count();
    echo "   Delivery areas count: $deliveryAreasCount\n";
    
    // Check for orphaned delivery areas
    $orphanedAreas = \Illuminate\Support\Facades\DB::table('simple_delivery_areas')
        ->leftJoin('companies', 'simple_delivery_areas.restaurant_id', '=', 'companies.id')
        ->whereNull('companies.id')
        ->count();
    
    if ($orphanedAreas > 0) {
        echo "   ✗ Found $orphanedAreas orphaned delivery areas\n";
    } else {
        echo "   ✓ No orphaned delivery areas found\n";
    }

    echo "\n=== Test completed successfully! ===\n";
    echo "Your database setup is now working correctly.\n";

} catch (Exception $e) {
    echo "\n✗ Error occurred: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n\n";
    
    if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
        echo "This appears to be a foreign key constraint error.\n";
        echo "Please check that all foreign key references point to the correct tables.\n";
    }
    
    exit(1);
}
