<?php return array (
  'providers' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    2 => 'Illuminate\\Bus\\BusServiceProvider',
    3 => 'Illuminate\\Cache\\CacheServiceProvider',
    4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    5 => 'Illuminate\\Cookie\\CookieServiceProvider',
    6 => 'Illuminate\\Database\\DatabaseServiceProvider',
    7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    10 => 'Illuminate\\Hashing\\HashServiceProvider',
    11 => 'Illuminate\\Mail\\MailServiceProvider',
    12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    15 => 'Illuminate\\Queue\\QueueServiceProvider',
    16 => 'Illuminate\\Redis\\RedisServiceProvider',
    17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    18 => 'Illuminate\\Session\\SessionServiceProvider',
    19 => 'Illuminate\\Translation\\TranslationServiceProvider',
    20 => 'Illuminate\\Validation\\ValidationServiceProvider',
    21 => 'Illuminate\\View\\ViewServiceProvider',
    22 => 'Akaunting\\Module\\Providers\\Laravel',
    23 => 'Akaunting\\Money\\Provider',
    24 => 'Barryvdh\\Debugbar\\ServiceProvider',
    25 => 'Berkayk\\OneSignal\\OneSignalServiceProvider',
    26 => 'Biscolab\\ReCaptcha\\ReCaptchaServiceProvider',
    27 => 'Cmixin\\BusinessDay\\Laravel\\ServiceProvider',
    28 => 'BusinessTime\\Laravel\\ServiceProvider',
    29 => 'Codedge\\Updater\\UpdaterServiceProvider',
    30 => 'dacoto\\SetEnv\\SetEnvServiceProvider',
    31 => 'Darryldecode\\Cart\\CartServiceProvider',
    32 => 'Dnsimmons\\OpenWeather\\OpenWeatherServiceProvider',
    33 => 'Facade\\Ignition\\IgnitionServiceProvider',
    34 => 'Fideloper\\Proxy\\TrustedProxyServiceProvider',
    35 => 'Fruitcake\\Cors\\CorsServiceProvider',
    36 => 'Intervention\\Image\\ImageServiceProvider',
    37 => 'JoeDixon\\Translation\\TranslationServiceProvider',
    38 => 'JoeDixon\\Translation\\TranslationBindingsServiceProvider',
    39 => 'KKomelin\\TranslatableStringExporter\\Providers\\ExporterServiceProvider',
    40 => 'LaravelFrontendPresets\\ArgonPreset\\ArgonPresetServiceProvider',
    41 => 'NotificationChannels\\OneSignal\\OneSignalServiceProvider',
    42 => 'NotificationChannels\\Twilio\\TwilioProvider',
    43 => 'Laravel\\Cashier\\CashierServiceProvider',
    44 => 'Illuminate\\Database\\Eloquent\\LegacyFactoryServiceProvider',
    45 => 'Laravel\\Sail\\SailServiceProvider',
    46 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    47 => 'Laravel\\Tinker\\TinkerServiceProvider',
    48 => 'Laravel\\Ui\\UiServiceProvider',
    49 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    50 => 'Mckenziearts\\Notify\\LaravelNotifyServiceProvider',
    51 => 'dacoto\\LaravelInstaller\\Providers\\LaravelDashboardInstallerProvider',
    52 => 'Mollie\\Laravel\\MollieServiceProvider',
    53 => 'Carbon\\Laravel\\ServiceProvider',
    54 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    55 => 'LaravelPWA\\Providers\\LaravelPWAServiceProvider',
    56 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    57 => 'Spatie\\Geocoder\\GeocoderServiceProvider',
    58 => 'Spatie\\CookieConsent\\CookieConsentServiceProvider',
    59 => 'Spatie\\GoogleTagManager\\GoogleTagManagerServiceProvider',
    60 => 'Spatie\\Permission\\PermissionServiceProvider',
    61 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    62 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    63 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    64 => 'willvincent\\Rateable\\RateableServiceProvider',
    65 => 'JoeDixon\\Translation\\TranslationServiceProvider',
    66 => 'Onecentlin\\Adminer\\ServiceProvider',
    67 => 'Akaunting\\Money\\Provider',
    68 => 'Intervention\\Image\\ImageServiceProvider',
    69 => 'Biscolab\\ReCaptcha\\ReCaptchaServiceProvider',
    70 => 'Darryldecode\\Cart\\CartServiceProvider',
    71 => 'App\\Providers\\AppServiceProvider',
    72 => 'App\\Providers\\AuthServiceProvider',
    73 => 'App\\Providers\\TranslationServiceProvider',
    74 => 'App\\Providers\\EventServiceProvider',
    75 => 'App\\Providers\\RouteServiceProvider',
    76 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    77 => 'Spatie\\Geocoder\\GeocoderServiceProvider',
    78 => 'Spatie\\CookieConsent\\CookieConsentServiceProvider',
    79 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    80 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Cookie\\CookieServiceProvider',
    2 => 'Illuminate\\Database\\DatabaseServiceProvider',
    3 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    4 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    5 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    6 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    7 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    8 => 'Illuminate\\Session\\SessionServiceProvider',
    9 => 'Illuminate\\View\\ViewServiceProvider',
    10 => 'Akaunting\\Module\\Providers\\Laravel',
    11 => 'Akaunting\\Money\\Provider',
    12 => 'Barryvdh\\Debugbar\\ServiceProvider',
    13 => 'Berkayk\\OneSignal\\OneSignalServiceProvider',
    14 => 'Biscolab\\ReCaptcha\\ReCaptchaServiceProvider',
    15 => 'Cmixin\\BusinessDay\\Laravel\\ServiceProvider',
    16 => 'BusinessTime\\Laravel\\ServiceProvider',
    17 => 'Codedge\\Updater\\UpdaterServiceProvider',
    18 => 'Darryldecode\\Cart\\CartServiceProvider',
    19 => 'Dnsimmons\\OpenWeather\\OpenWeatherServiceProvider',
    20 => 'Facade\\Ignition\\IgnitionServiceProvider',
    21 => 'Fideloper\\Proxy\\TrustedProxyServiceProvider',
    22 => 'Fruitcake\\Cors\\CorsServiceProvider',
    23 => 'Intervention\\Image\\ImageServiceProvider',
    24 => 'JoeDixon\\Translation\\TranslationServiceProvider',
    25 => 'KKomelin\\TranslatableStringExporter\\Providers\\ExporterServiceProvider',
    26 => 'LaravelFrontendPresets\\ArgonPreset\\ArgonPresetServiceProvider',
    27 => 'NotificationChannels\\OneSignal\\OneSignalServiceProvider',
    28 => 'Laravel\\Cashier\\CashierServiceProvider',
    29 => 'Illuminate\\Database\\Eloquent\\LegacyFactoryServiceProvider',
    30 => 'Laravel\\Ui\\UiServiceProvider',
    31 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    32 => 'Mckenziearts\\Notify\\LaravelNotifyServiceProvider',
    33 => 'dacoto\\LaravelInstaller\\Providers\\LaravelDashboardInstallerProvider',
    34 => 'Mollie\\Laravel\\MollieServiceProvider',
    35 => 'Carbon\\Laravel\\ServiceProvider',
    36 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    37 => 'LaravelPWA\\Providers\\LaravelPWAServiceProvider',
    38 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    39 => 'Spatie\\Geocoder\\GeocoderServiceProvider',
    40 => 'Spatie\\CookieConsent\\CookieConsentServiceProvider',
    41 => 'Spatie\\GoogleTagManager\\GoogleTagManagerServiceProvider',
    42 => 'Spatie\\Permission\\PermissionServiceProvider',
    43 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    44 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    45 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    46 => 'willvincent\\Rateable\\RateableServiceProvider',
    47 => 'JoeDixon\\Translation\\TranslationServiceProvider',
    48 => 'Onecentlin\\Adminer\\ServiceProvider',
    49 => 'Akaunting\\Money\\Provider',
    50 => 'Intervention\\Image\\ImageServiceProvider',
    51 => 'Biscolab\\ReCaptcha\\ReCaptchaServiceProvider',
    52 => 'Darryldecode\\Cart\\CartServiceProvider',
    53 => 'App\\Providers\\AppServiceProvider',
    54 => 'App\\Providers\\AuthServiceProvider',
    55 => 'App\\Providers\\TranslationServiceProvider',
    56 => 'App\\Providers\\EventServiceProvider',
    57 => 'App\\Providers\\RouteServiceProvider',
    58 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    59 => 'Spatie\\Geocoder\\GeocoderServiceProvider',
    60 => 'Spatie\\CookieConsent\\CookieConsentServiceProvider',
    61 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    62 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
  ),
  'deferred' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastManager' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Factory' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Broadcaster' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\QueueingDispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\BatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'cache' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.store' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.psr6' => 'Illuminate\\Cache\\CacheServiceProvider',
    'memcached.connector' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Cache\\RateLimiter' => 'Illuminate\\Cache\\CacheServiceProvider',
    'command.cache.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.cache.forget' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.clear-compiled' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.auth.resets.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.config.cache' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.config.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\DbCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.db.prune' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.db.wipe' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.down' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.environment' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.event.cache' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.event.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.event.list' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.key.generate' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.optimize' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.optimize.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.package.discover' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.failed' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.flush' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.forget' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.listen' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.monitor' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.prune-batches' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.prune-failed-jobs' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.restart' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.retry' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.retry-batch' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.work' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.route.cache' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.route.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.route.list' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.schema.dump' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.seed' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleFinishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleRunCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleClearCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleTestCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleWorkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.storage.link' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.up' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.view.cache' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.view.clear' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.cache.table' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.cast.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.channel.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.component.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.console.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.controller.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.event.generate' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.event.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.exception.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.factory.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.job.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.listener.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.mail.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.middleware.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.model.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.notification.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.notification.table' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.observer.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.policy.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.provider.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.failed-table' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.table' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.queue.batches-table' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.request.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.resource.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.rule.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.seeder.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.session.table' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.serve' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.stub.publish' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.test.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.vendor.publish' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migrator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.repository' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.creator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate.fresh' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate.install' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate.refresh' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate.reset' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate.rollback' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate.status' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'command.migrate.make' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'composer' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'hash' => 'Illuminate\\Hashing\\HashServiceProvider',
    'hash.driver' => 'Illuminate\\Hashing\\HashServiceProvider',
    'mail.manager' => 'Illuminate\\Mail\\MailServiceProvider',
    'mailer' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Mail\\Markdown' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Contracts\\Pipeline\\Hub' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'queue' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.connection' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.failer' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.listener' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.worker' => 'Illuminate\\Queue\\QueueServiceProvider',
    'redis' => 'Illuminate\\Redis\\RedisServiceProvider',
    'redis.connection' => 'Illuminate\\Redis\\RedisServiceProvider',
    'auth.password' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'auth.password.broker' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'translator' => 'JoeDixon\\Translation\\TranslationBindingsServiceProvider',
    'translation.loader' => 'JoeDixon\\Translation\\TranslationBindingsServiceProvider',
    'validator' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'validation.presence' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'NotificationChannels\\Twilio\\TwilioConfig' => 'NotificationChannels\\Twilio\\TwilioProvider',
    'Twilio\\Rest\\Client' => 'NotificationChannels\\Twilio\\TwilioProvider',
    'NotificationChannels\\Twilio\\TwilioChannel' => 'NotificationChannels\\Twilio\\TwilioProvider',
    'Laravel\\Sail\\Console\\InstallCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Sail\\Console\\PublishCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Socialite\\Contracts\\Factory' => 'Laravel\\Socialite\\SocialiteServiceProvider',
    'command.tinker' => 'Laravel\\Tinker\\TinkerServiceProvider',
  ),
  'when' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastServiceProvider' => 
    array (
    ),
    'Illuminate\\Bus\\BusServiceProvider' => 
    array (
    ),
    'Illuminate\\Cache\\CacheServiceProvider' => 
    array (
    ),
    'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider' => 
    array (
    ),
    'Illuminate\\Hashing\\HashServiceProvider' => 
    array (
    ),
    'Illuminate\\Mail\\MailServiceProvider' => 
    array (
    ),
    'Illuminate\\Pipeline\\PipelineServiceProvider' => 
    array (
    ),
    'Illuminate\\Queue\\QueueServiceProvider' => 
    array (
    ),
    'Illuminate\\Redis\\RedisServiceProvider' => 
    array (
    ),
    'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider' => 
    array (
    ),
    'Illuminate\\Translation\\TranslationServiceProvider' => 
    array (
    ),
    'Illuminate\\Validation\\ValidationServiceProvider' => 
    array (
    ),
    'dacoto\\SetEnv\\SetEnvServiceProvider' => 
    array (
    ),
    'JoeDixon\\Translation\\TranslationBindingsServiceProvider' => 
    array (
    ),
    'NotificationChannels\\Twilio\\TwilioProvider' => 
    array (
    ),
    'Laravel\\Sail\\SailServiceProvider' => 
    array (
    ),
    'Laravel\\Socialite\\SocialiteServiceProvider' => 
    array (
    ),
    'Laravel\\Tinker\\TinkerServiceProvider' => 
    array (
    ),
  ),
);