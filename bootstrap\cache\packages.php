<?php return array (
  'akaunting/module' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\Module\\Providers\\Laravel',
    ),
    'aliases' => 
    array (
      'Module' => 'Akaunting\\Module\\Facade',
    ),
  ),
  'akaunting/money' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\Money\\Provider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
  ),
  'berkayk/onesignal-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Berkayk\\OneSignal\\OneSignalServiceProvider',
    ),
    'aliases' => 
    array (
      'OneSignal' => 'Berkayk\\OneSignal\\OneSignalFacade',
    ),
  ),
  'biscolab/laravel-recaptcha' => 
  array (
    'providers' => 
    array (
      0 => 'Biscolab\\ReCaptcha\\ReCaptchaServiceProvider',
    ),
    'aliases' => 
    array (
      'ReCaptcha' => 'Biscolab\\ReCaptcha\\Facades\\ReCaptcha',
    ),
  ),
  'cmixin/business-day' => 
  array (
    'providers' => 
    array (
      0 => 'Cmixin\\BusinessDay\\Laravel\\ServiceProvider',
    ),
  ),
  'cmixin/business-time' => 
  array (
    'providers' => 
    array (
      0 => 'BusinessTime\\Laravel\\ServiceProvider',
    ),
  ),
  'codedge/laravel-selfupdater' => 
  array (
    'providers' => 
    array (
      0 => 'Codedge\\Updater\\UpdaterServiceProvider',
    ),
    'aliases' => 
    array (
      'Updater' => 'Codedge\\Updater\\UpdaterFacade',
    ),
  ),
  'dacoto/laravel-setenv' => 
  array (
    'providers' => 
    array (
      0 => 'dacoto\\SetEnv\\SetEnvServiceProvider',
    ),
    'aliases' => 
    array (
      'SetEnv' => 'dacoto\\SetEnv\\Facades\\SetEnv',
    ),
  ),
  'darryldecode/cart' => 
  array (
    'providers' => 
    array (
      0 => 'Darryldecode\\Cart\\CartServiceProvider',
    ),
    'aliases' => 
    array (
      'Cart' => 'Darryldecode\\Cart\\Facades\\CartFacade',
    ),
  ),
  'dnsimmons/openweather' => 
  array (
    'providers' => 
    array (
      0 => 'Dnsimmons\\OpenWeather\\OpenWeatherServiceProvider',
    ),
    'aliases' => 
    array (
      'OpenWeather' => 'Dnsimmons\\OpenWeather\\OpenWeather',
    ),
  ),
  'facade/ignition' => 
  array (
    'providers' => 
    array (
      0 => 'Facade\\Ignition\\IgnitionServiceProvider',
    ),
    'aliases' => 
    array (
      'Flare' => 'Facade\\Ignition\\Facades\\Flare',
    ),
  ),
  'fideloper/proxy' => 
  array (
    'providers' => 
    array (
      0 => 'Fideloper\\Proxy\\TrustedProxyServiceProvider',
    ),
  ),
  'fruitcake/laravel-cors' => 
  array (
    'providers' => 
    array (
      0 => 'Fruitcake\\Cors\\CorsServiceProvider',
    ),
  ),
  'intervention/image' => 
  array (
    'providers' => 
    array (
      0 => 'Intervention\\Image\\ImageServiceProvider',
    ),
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Facades\\Image',
    ),
  ),
  'joedixon/laravel-translation' => 
  array (
    'providers' => 
    array (
      0 => 'JoeDixon\\Translation\\TranslationServiceProvider',
      1 => 'JoeDixon\\Translation\\TranslationBindingsServiceProvider',
    ),
  ),
  'kkomelin/laravel-translatable-string-exporter' => 
  array (
    'providers' => 
    array (
      0 => 'KKomelin\\TranslatableStringExporter\\Providers\\ExporterServiceProvider',
    ),
  ),
  'laravel-frontend-presets/argon' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelFrontendPresets\\ArgonPreset\\ArgonPresetServiceProvider',
    ),
  ),
  'laravel-notification-channels/onesignal' => 
  array (
    'providers' => 
    array (
      0 => 'NotificationChannels\\OneSignal\\OneSignalServiceProvider',
    ),
  ),
  'laravel-notification-channels/twilio' => 
  array (
    'providers' => 
    array (
      0 => 'NotificationChannels\\Twilio\\TwilioProvider',
    ),
  ),
  'laravel/cashier' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Cashier\\CashierServiceProvider',
    ),
  ),
  'laravel/legacy-factories' => 
  array (
    'providers' => 
    array (
      0 => 'Illuminate\\Database\\Eloquent\\LegacyFactoryServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
  ),
  'mckenziearts/laravel-notify' => 
  array (
    'providers' => 
    array (
      0 => 'Mckenziearts\\Notify\\LaravelNotifyServiceProvider',
    ),
    'aliases' => 
    array (
      'Notify' => 'Mckenziearts\\Notify\\Facades\\LaravelNotify',
    ),
  ),
  'mobidonia/laravel-dashboard-installer' => 
  array (
    'providers' => 
    array (
      0 => 'dacoto\\LaravelInstaller\\Providers\\LaravelDashboardInstallerProvider',
    ),
  ),
  'mollie/laravel-mollie' => 
  array (
    'providers' => 
    array (
      0 => 'Mollie\\Laravel\\MollieServiceProvider',
    ),
    'aliases' => 
    array (
      'Mollie' => 'Mollie\\Laravel\\Facades\\Mollie',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'silviolleite/laravelpwa' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelPWA\\Providers\\LaravelPWAServiceProvider',
    ),
  ),
  'spatie/eloquent-sortable' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    ),
  ),
  'spatie/geocoder' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Geocoder\\GeocoderServiceProvider',
    ),
    'aliases' => 
    array (
      'Geocoder' => 'Spatie\\Geocoder\\Facades\\Geocoder',
    ),
  ),
  'spatie/laravel-cookie-consent' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\CookieConsent\\CookieConsentServiceProvider',
    ),
  ),
  'spatie/laravel-googletagmanager' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\GoogleTagManager\\GoogleTagManagerServiceProvider',
    ),
    'aliases' => 
    array (
      'GoogleTagManager' => 'Spatie\\GoogleTagManager\\GoogleTagManagerFacade',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'spatie/laravel-sitemap' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    ),
  ),
  'spatie/laravel-translatable' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    ),
  ),
  'unicodeveloper/laravel-paystack' => 
  array (
    'providers' => 
    array (
      0 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    ),
    'aliases' => 
    array (
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
  ),
  'willvincent/laravel-rateable' => 
  array (
    'providers' => 
    array (
      0 => 'willvincent\\Rateable\\RateableServiceProvider',
    ),
  ),
);